// main.rs
pub mod dataset;
mod financial_bert;
use candle_bert_time_series::batcher::Batcher;
use dataset::load_and_prepare_data;
use financial_bert::{Config, FinancialTransformerForMaskedRegression};

use candle_core::{DType, Device, Result, Tensor};
use candle_nn::{loss, Optimizer, VarBuilder, VarMap};

// --- Configuration ---
// NUM_TIME_SERIES will be determined dynamically from the data
const SEQUENCE_LENGTH: usize = 120;
const MODEL_DIMS: usize = 256;
const NUM_LAYERS: usize = 6;
const NUM_HEADS: usize = 8;
const NUM_EPOCHS: usize = 50;
const LEARNING_RATE: f64 = 1e-4;
const MASK_PROB: f32 = 0.15;
const BATCH_SIZE: usize = 32;

// Data file path - update this to point to your parquet file
const DATA_PATH: &str = "/home/<USER>/Downloads/transformed_dataset.parquet";

// --- Data Generation and Preparation ---

/// Corrected and simplified masking function for 3D batched input.
fn mask_data(
    input: &Tensor, // Expects a 3D tensor: [BATCH_SIZE, SEQUENCE_LENGTH, NUM_TIME_SERIES] -> [32, 120, 190]
    device: &Device,
) -> Result<(Tensor, Tensor, Tensor)> {
    let shape = input.shape();
    let rand_mask = Tensor::rand(0f32, 1f32, shape, device)?;
    // `mask` is a boolean tensor of shape [32, 120, 190]
    let mask = (rand_mask.lt(MASK_PROB))?;
    let zeros = Tensor::zeros(shape, input.dtype(), device)?;

    // `masked_select` correctly gathers only the masked values into a 1D tensor.
    // Shape: [num_masked_elements] (flattened from all batch elements)
    let true_labels = mask.where_cond(input, &zeros)?;

    // Zero out the values in the input where the mask is true.
    // This preserves the original shape of the input.
    // Shape: [32, 120, 190]
    // 1. Create a tensor of ones with the same shape as the mask.
    //    The `lt` operation produces a U8 tensor, so we use DType::U8.
    let ones = Tensor::ones(shape, DType::U8, device)?;
    // 2. Subtract the mask from ones. If mask is [1, 0, 1], result is [0, 1, 0].
    let inverted_mask = ones.sub(&mask)?;
    // 3. Use this inverted mask to create the model input.
    let masked_input = input.broadcast_mul(&inverted_mask.to_dtype(DType::F32)?)?;

    Ok((masked_input, true_labels, mask))
}

// --- The Main Training Function ---

fn main() -> Result<()> {
    let device = Device::cuda_if_available(0)?;
    println!("Training on device: {:?}", device);

    // --- Data Loading First to Determine Dimensions ---
    println!("Loading cryptocurrency data...");
    let (full_data_sequence, num_time_series) = load_and_prepare_data(DATA_PATH, &device)?;
    let total_timesteps = full_data_sequence.dims()[0];
    let train_split = (total_timesteps as f32 * 0.8) as usize;
    let train_data = full_data_sequence.narrow(0, 0, train_split)?;

    println!("Detected {} cryptocurrencies in the dataset", num_time_series);

    // --- Model and Optimizer Setup ---
    let config = Config {
        num_time_series,
        hidden_size: MODEL_DIMS,
        num_hidden_layers: NUM_LAYERS,
        num_attention_heads: NUM_HEADS,
        intermediate_size: MODEL_DIMS * 4,
        hidden_act: financial_bert::HiddenAct::Gelu,
        hidden_dropout_prob: 0.1,
        max_position_embeddings: SEQUENCE_LENGTH,
        initializer_range: 0.02,
        layer_norm_eps: 1e-12,
        position_embedding_type: financial_bert::PositionEmbeddingType::Absolute,
        use_cache: false,
        model_type: Some("financial_transformer".to_string()),
    };
    let varmap = VarMap::new();
    let vb = VarBuilder::from_varmap(&varmap, DType::F32, &device);
    let model = FinancialTransformerForMaskedRegression::load(vb, &config)?;

    let adamw_params = candle_nn::ParamsAdamW {
        lr: LEARNING_RATE,
        ..Default::default()
    };
    let mut optimizer = candle_nn::AdamW::new(varmap.all_vars(), adamw_params)?;

    println!("Starting training...");
    for epoch in 0..NUM_EPOCHS {
        // --- Batch Preparation ---
        // `batch` Shape: [BATCH_SIZE, SEQUENCE_LENGTH, NUM_TIME_SERIES] -> [32, 120, 190]
        let batch = Batcher::new(&train_data, SEQUENCE_LENGTH, BATCH_SIZE).next().ok_or_else(|| candle_core::Error::Msg("No batch available".to_string()))??;

        // `masked_input` Shape: [32, 120, 190]
        // `true_labels` Shape: [num_masked] (1D, flattened from all batch elements)
        // `mask` Shape: [32, 120, 190] (boolean)
        let (masked_input, true_labels, mask) = mask_data(&batch, &device)?;

        // --- FORWARD PASS ---
        // The model expects a 3D tensor: (batch_size, seq_len, features).
        // The batch is already properly shaped, no need to unsqueeze.
        // `model_input` Shape: [32, 120, 190]
        let model_input = masked_input;

        // `predictions` will have the same shape as the input.
        // `predictions` Shape: [32, 120, 190]
        let predictions = model.forward(&model_input)?;

        // --- LOSS CALCULATION ---
        // To compare with our labels, we must isolate the predictions at the masked positions.
        // Use the same boolean mask to select the predicted values.
        // This flattens the tensor, matching the shape of `true_labels`.
        // `predicted_values` Shape: [num_masked] (1D, flattened from all batch elements)
        let zeros = Tensor::zeros(predictions.shape(), predictions.dtype(), &device)?;
        let predicted_values = mask.where_cond(&predictions, &zeros)?;

        // Now we can compare the two 1D tensors.
        let loss = loss::mse(&predicted_values, &true_labels)?;

        // --- BACKWARD PASS ---
        optimizer.backward_step(&loss)?;

        println!(
            "Epoch: {:4} | Loss: {:8.5}",
            epoch,
            loss.to_scalar::<f32>()?
        );

        // Save model checkpoint after each epoch
        let checkpoint_path = format!("current_model.safetensors");
        varmap.save(&checkpoint_path)?;
        println!("Saved checkpoint to: {}", checkpoint_path);
    }

    Ok(())
}